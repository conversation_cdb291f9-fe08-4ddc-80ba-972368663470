export interface Team {
    id: number;
    name: string;
}

export interface Match {
    home_team: Team;
    away_team: Team;
    week: number;
    home_score?: number;
    away_score?: number;
    is_played?: boolean;
}

export interface Standing {
    team: string;
    played: number;
    won: number;
    drawn: number;
    lost: number;
    gd: number;
    points: number;
}

export interface SimulationProps {
    fixtures: Record<string, Match[]>;
    standings: Standing[];
    predictions: ChampionshipPredictions;
    current_week: number;
    season_complete: boolean;
    seasonId: string;
}


export interface ChampionshipPredictions {
    [teamName: string]: number;
}
