<?php

namespace App\Services;

use App\DTOs\SimulationResultDTO;
use App\DTOs\StandingDTO;
use App\Http\Resources\LeagueMatchResource;
use App\Models\LeagueMatches;
use Illuminate\Support\Collection;

class SimulationService
{

    const MAX_WEEKS_LEAGUE = 6;

    private function getSeasonData(string $seasonId): Collection
    {
        return LeagueMatches::with(['homeTeam', 'awayTeam'])
            ->where('season_id', $seasonId)
            ->orderBy('week')
            ->orderBy('id')
            ->get();
    }


    public function getSimulationResult(string $seasonId): SimulationResultDTO
    {
        $allMatches = $this->getSeasonData($seasonId);

        $standings = $this->calculateStandingsFromMatches($allMatches);
        $currentWeek = $this->getCurrentWeekFromMatches($allMatches);
        $predictions = $this->calculateChampionshipPredictions($standings, $currentWeek);
        $seasonComplete = $this->isSeasonCompleteFromMatches($allMatches);

        $fixtures = $this->transformMatchesToFixtures($allMatches);

        return new SimulationResultDTO(
            $standings,
            $currentWeek,
            $predictions,
            $seasonComplete,
            $fixtures,
            $seasonId
        );
    }

    public function simulateMatchAsPlayed(LeagueMatches $match): void
    {
        $homePower = rand(1, 100);
        $awayPower = rand(1, 100);

        $result = $this->simulateMatchResult($homePower, $awayPower);

        $update = $match->update([
            'home_score' => $result['home_score'],
            'away_score' => $result['away_score'],
            'is_played' => true,
            'played_at' => now(),
        ]);

        if (!$update) {
            throw new \Exception('Failed to update match');
        }
    }

    private function simulateMatchResult(int $homePower, int $awayPower): array
    {
        $totalPower = $homePower + $awayPower;
        $homeWinChance = ($homePower / $totalPower) * 100;

        $random = rand(1, 100);

        if ($random <= $homeWinChance) {
            // Home team wins
            $homeScore = rand(1, 4);
            $awayScore = rand(0, $homeScore - 1);
        } elseif ($random <= $homeWinChance + 15) {
            // Draw (15% chance regardless of power difference)
            $score = rand(0, 3);
            $homeScore = $score;
            $awayScore = $score;
        } else {
            // Away team wins
            $awayScore = rand(1, 4);
            $homeScore = rand(0, $awayScore - 1);
        }

        return [
            'home_score' => $homeScore,
            'away_score' => $awayScore
        ];
    }

    private function calculateStandingsFromMatches(Collection $allMatches): array
    {
        $teamStats = [];
        $playedMatches = $allMatches->where('is_played', true);

        foreach ($allMatches as $match) {
            if (!isset($teamStats[$match->homeTeam->name])) {
                $teamStats[$match->homeTeam->name] = new StandingDTO($match->homeTeam->name);
            }
            if (!isset($teamStats[$match->awayTeam->name])) {
                $teamStats[$match->awayTeam->name] = new StandingDTO($match->awayTeam->name);
            }
        }

        foreach ($playedMatches as $match) {
            $homeTeam = $teamStats[$match->homeTeam->name];
            $awayTeam = $teamStats[$match->awayTeam->name];
            $homeScore = $match->home_score ?? 0;
            $awayScore = $match->away_score ?? 0;

            if ($homeScore > $awayScore) {
                $homeTeam->addWin();
                $awayTeam->addLoss();
            } elseif ($homeScore < $awayScore) {
                $awayTeam->addWin();
                $homeTeam->addLoss();
            } else {
                $homeTeam->addDraw();
                $awayTeam->addDraw();
            }
        }

        $standings = array_map(fn(StandingDTO $dto) => $dto->toArray(), array_values($teamStats));
        usort($standings, function ($a, $b) {
            return $b['points'] - $a['points'];
        });

        return $standings;
    }

    public function calculateChampionshipPredictions(array $standings, int $currentWeek): array
    {
        // Only calculate predictions if current week > 3
        if ($currentWeek <= 3) {
            return [];
        }

        $totalPoints = array_sum(array_column($standings, 'points'));
        $predictions = [];

        if ($totalPoints === 0) {
            $equalChance = round(100 / count($standings));
            foreach ($standings as $team) {
                $predictions[$team['team']] = $equalChance;
            }
        } else {
            foreach ($standings as $index => $team) {
                $baseChance = $totalPoints > 0 ? ($team['points'] / $totalPoints) * 100 : 25;
                $positionBonus = (count($standings) - $index) * 5;
                $predictions[$team['team']] = min(round($baseChance + $positionBonus), 100);
            }
        }

        // Sort predictions by percentage
        arsort($predictions);

        return $predictions;
    }

    private function getCurrentWeekFromMatches(Collection $allMatches): int
    {
        $unplayedMatches = $allMatches->where('is_played', false);

        if ($unplayedMatches->isEmpty()) {
            return $allMatches->max('week');
        }

        return $unplayedMatches->min('week');
    }


    private function isSeasonCompleteFromMatches(Collection $allMatches): bool
    {
        $maxWeek = $allMatches->max('week') ?? 0;

        if ($maxWeek < self::MAX_WEEKS_LEAGUE) {
            return false;
        }

        $lastWeekMatches = $allMatches->where('week', $maxWeek);
        return $lastWeekMatches->every(fn($match) => $match->is_played);
    }


    private function transformMatchesToFixtures(Collection $allMatches): array
    {
        return LeagueMatchResource::groupByWeek($allMatches);
    }


}
